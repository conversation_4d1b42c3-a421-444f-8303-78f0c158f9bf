{"name": "@toolsdk.ai/example", "version": "1.9.3-beta.16", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@bika/contents": "workspace:*", "@bika/ui": "workspace:*", "@toolsdk.ai/sdk-ts": "workspace:*", "ai": "^5", "next": "15.3.3", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^9", "eslint-config-next": "15.3.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}