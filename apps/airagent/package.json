{"name": "airagent", "private": true, "version": "1.9.3-beta.16", "type": "module", "scripts": {"dev": "next dev --port 3005", "build": "next build", "start": "next start", "lint": "next lint", "tauri": "tauri"}, "dependencies": {"@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "next": "^15.3.3", "react": "18.3.1", "react-dom": "18.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@tauri-apps/cli": "^2", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^9", "eslint-config-next": "^15.3.3", "typescript": "~5.8.3"}}